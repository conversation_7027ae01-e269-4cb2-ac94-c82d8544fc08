<?php

namespace App\Dto;

class CourseDto
{
    /**
     * @var int
     */
    public $id;

    /**
     * @var string
     */
    public $title;

    /**
     * @var string
     */
    public $url;

    /**
     * @var int
     */
    public $completionRatio;

    /**
     * @var string
     */
    public $lastAccessedTime;

    /**
     * @var string
     */
    public $enrollmentTime;

    /**
     * @var int
     */
    public $numberOfCollections;

    /**
     * @var float
     */
    public $rating;

    /**
     * @var int
     */
    public $numSubscribers;

    /**
     * @var string
     */
    public $courseLength;

    /**
     * CourseDto constructor.
     * Normalize course object reading selected props
     * @param array $course
     */
    public function __construct(array $course)
    {
        $this->id = $course['id'];
        $this->title = $course['title'];
        $this->url = 'https://udemy.com' . $course['url'];
        $this->completionRatio = $course['completion_ratio'];
        $this->lastAccessedTime = $course['last_accessed_time'];
        $this->enrollmentTime = $course['enrollment_time'];
        $this->numberOfCollections = $course['num_collections'];

        // New fields - handle cases where they might not be present
        $this->rating = isset($course['rating']) ? $course['rating'] : 0;
        $this->numSubscribers = isset($course['num_subscribers']) ? $course['num_subscribers'] : 0;

        // Course length might be in different fields, try multiple possibilities
        $this->courseLength = $this->extractCourseLength($course);
    }

    /**
     * Extract course length from various possible fields in the API response
     * @param array $course
     * @return string
     */
    private function extractCourseLength(array $course)
    {
        // Try different possible field names for course length
        if (isset($course['content_length_practice_test_questions'])) {
            return $course['content_length_practice_test_questions'];
        }

        if (isset($course['content_info']) && is_array($course['content_info'])) {
            if (isset($course['content_info']['length'])) {
                return $course['content_info']['length'];
            }
            if (isset($course['content_info']['duration'])) {
                return $course['content_info']['duration'];
            }
        }

        if (isset($course['content_length'])) {
            return $course['content_length'];
        }

        if (isset($course['duration'])) {
            return $course['duration'];
        }

        return 'N/A';
    }
}